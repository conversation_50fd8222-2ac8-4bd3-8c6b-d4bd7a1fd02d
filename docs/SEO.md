# SEO Implementation Guide

This guide explains how to implement comprehensive SEO in your TanStack Router application.

## Overview

Our SEO implementation follows TanStack Router's document head management best practices and includes:

- **Meta tags** for basic SEO
- **Open Graph** tags for social media sharing
- **Twitter Card** tags for Twitter sharing
- **Structured data** (JSON-LD) for rich snippets
- **Canonical URLs** for duplicate content prevention
- **Robots meta** for search engine crawling control

## Basic Usage

### 1. Route-Level SEO

Add SEO meta tags to any route using the `head` function:

```tsx
import { createFileRoute } from '@tanstack/react-router'
import { seo } from '~/utils/seo'

export const Route = createFileRoute('/about')({
  head: () => ({
    meta: [
      ...seo({
        title: 'About Us - Your App',
        description: 'Learn more about our company and mission.',
        keywords: 'about, company, mission, team',
        url: 'https://yourapp.com/about',
        canonical: 'https://yourapp.com/about',
      }),
    ],
  }),
  component: AboutPage,
})
```

### 2. Dynamic SEO for Data-Driven Pages

For pages with dynamic content (like blog posts):

```tsx
export const Route = createFileRoute('/blog/$slug')({
  loader: async ({ params }) => {
    const post = await fetchPost(params.slug)
    return { post }
  },
  head: ({ loaderData }) => ({
    meta: [
      ...seo({
        title: `${loaderData.post.title} - Your Blog`,
        description: loaderData.post.excerpt,
        keywords: loaderData.post.tags.join(', '),
        type: 'article',
        author: loaderData.post.author,
        publishedTime: loaderData.post.publishedAt,
        image: loaderData.post.featuredImage,
        url: `https://yourapp.com/blog/${loaderData.post.slug}`,
      }),
    ],
  }),
  component: BlogPost,
})
```

## Advanced Features

### Structured Data (JSON-LD)

Add structured data for rich snippets in search results:

```tsx
import { createStructuredDataScript } from '~/utils/seo'

export const Route = createFileRoute('/products/$id')({
  head: ({ loaderData }) => ({
    scripts: [
      createStructuredDataScript({
        type: 'Product',
        name: loaderData.product.name,
        description: loaderData.product.description,
        image: loaderData.product.images,
        // Add more product-specific structured data
      }),
    ],
  }),
})
```

### Breadcrumb Structured Data

For better navigation understanding:

```tsx
scripts: [
  createStructuredDataScript({
    type: 'BreadcrumbList',
    breadcrumbs: [
      { name: 'Home', url: 'https://yourapp.com' },
      { name: 'Products', url: 'https://yourapp.com/products' },
      { name: product.name, url: `https://yourapp.com/products/${product.id}` },
    ],
  }),
]
```

## SEO Configuration Options

### Basic Options

- `title` (required): Page title
- `description`: Meta description (recommended 150-160 characters)
- `keywords`: Comma-separated keywords
- `url`: Canonical URL of the page
- `canonical`: Canonical URL (if different from url)

### Open Graph Options

- `type`: 'website', 'article', 'product', 'profile'
- `siteName`: Name of your website
- `image`: Featured image URL
- `locale`: Language locale (default: 'en_US')

### Twitter Card Options

- `twitterCard`: 'summary', 'summary_large_image', 'app', 'player'
- `twitterSite`: @username of website
- `twitterCreator`: @username of content creator

### Article-Specific Options

- `author`: Author name
- `publishedTime`: Publication date (ISO format)
- `modifiedTime`: Last modified date (ISO format)
- `section`: Article section/category
- `tags`: Array of article tags

### Technical Options

- `robots`: Search engine crawling instructions (default: 'index, follow')
- `alternateLocales`: Array of alternate language locales

## Best Practices

### 1. Title Optimization

- Keep titles under 60 characters
- Include primary keyword near the beginning
- Make titles unique for each page
- Use a consistent format: "Page Title - Site Name"

### 2. Meta Descriptions

- Keep between 150-160 characters
- Include primary and secondary keywords naturally
- Write compelling copy that encourages clicks
- Make each description unique

### 3. Image Optimization

- Use high-quality images (1200x630px for Open Graph)
- Include alt text in image tags
- Optimize file sizes for fast loading
- Use descriptive file names

### 4. URL Structure

- Use clean, descriptive URLs
- Include keywords in URLs
- Use hyphens to separate words
- Keep URLs as short as possible while being descriptive

## Testing Your SEO

### Tools for Testing

1. **Google Search Console**: Monitor search performance
2. **Facebook Sharing Debugger**: Test Open Graph tags
3. **Twitter Card Validator**: Test Twitter cards
4. **Google Rich Results Test**: Test structured data
5. **Lighthouse**: Overall SEO audit

### Local Testing

You can test your meta tags locally by viewing the page source or using browser developer tools to inspect the `<head>` section.

## Common Issues and Solutions

### 1. Meta Tags Not Updating

- Ensure you're using the `head` function in your route
- Check that the `<HeadContent />` component is rendered in your root layout
- Clear browser cache and social media caches

### 2. Social Media Not Showing Correct Image

- Verify image URL is absolute (not relative)
- Check image dimensions (1200x630px recommended)
- Use Facebook Sharing Debugger to refresh cache

### 3. Structured Data Errors

- Validate JSON-LD using Google's Rich Results Test
- Ensure all required properties are included
- Check for proper JSON formatting

## Migration from Other SEO Solutions

If you're migrating from other SEO solutions like React Helmet:

1. Remove React Helmet dependencies
2. Replace `<Helmet>` components with route `head` functions
3. Update meta tag syntax to match TanStack Router format
4. Test all pages to ensure meta tags are working correctly

## Performance Considerations

- Meta tags are rendered server-side, improving initial page load SEO
- Structured data is minimal and doesn't significantly impact performance
- Images should be optimized and served from a CDN when possible
- Consider lazy loading non-critical SEO images
