import React from 'react'
import { seo, type SEOProps } from '~/utils/seo'

interface SEOComponentProps extends SEOProps {
  children?: React.ReactNode
}

/**
 * SEO Component for managing document head meta tags
 *
 * This component provides a convenient way to set SEO meta tags
 * for individual pages. It uses the seo utility function to generate
 * the appropriate meta tags according to TanStack Router's head management.
 *
 * @example
 * ```tsx
 * // In a route file
 * export const Route = createFileRoute('/about')({
 *   head: () => ({
 *     meta: seo({
 *       title: 'About Us - Your App',
 *       description: 'Learn more about our company and mission.',
 *       keywords: 'about, company, mission, team',
 *       url: 'https://yourapp.com/about',
 *     }),
 *   }),
 *   component: AboutPage,
 * })
 * ```
 */
const SEO: React.FC<SEOComponentProps> = ({ children }) => {
  // This component doesn't render anything in the DOM
  // It's meant to be used with TanStack Router's head management
  // The actual SEO tags are handled by the route's head function

  if (children) {
    return <>{children}</>
  }

  return null
}

/**
 * Hook to generate SEO meta tags for use in route head functions
 *
 * @example
 * ```tsx
 * export const Route = createFileRoute('/blog/$slug')({
 *   head: ({ params }) => ({
 *     meta: useSEO({
 *       title: `${post.title} - Your Blog`,
 *       description: post.excerpt,
 *       type: 'article',
 *       author: post.author,
 *       publishedTime: post.publishedAt,
 *       image: post.featuredImage,
 *       url: `https://yourapp.com/blog/${params.slug}`,
 *     }),
 *   }),
 * })
 * ```
 */
export const useSEO = (props: SEOProps) => {
  return seo(props)
}

/**
 * Default SEO configuration for the application
 */
export const defaultSEO: Partial<SEOProps> = {
  siteName: 'Your App',
  type: 'website',
  locale: 'en_US',
  twitterCard: 'summary_large_image',
  robots: 'index, follow',
}

/**
 * Helper function to merge default SEO with page-specific SEO
 */
export const mergeSEO = (pageSEO: Partial<SEOProps>): SEOProps => {
  return {
    ...defaultSEO,
    ...pageSEO,
    title: pageSEO.title || 'Your App',
  } as SEOProps
}

export default SEO