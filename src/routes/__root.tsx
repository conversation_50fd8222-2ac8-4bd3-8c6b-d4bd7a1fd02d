import {
  HeadContent,
  Link,
  Outlet,
  <PERSON>rip<PERSON>,
  createRootRoute,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import appCss from '~/styles/app.css?url'
import { seo, createStructuredDataScript } from '~/utils/seo'

export const Route = createRootRoute({
  head: () => ({
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'Your App - Modern Web Application',
        description: 'A modern, fast, and scalable web application built with TanStack Router and React.',
        keywords: 'react, tanstack, router, web app, modern, fast, scalable',
        siteName: 'Your App',
        type: 'website',
        locale: 'en_US',
        robots: 'index, follow',
        twitterCard: 'summary_large_image',
        author: 'Your Name',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
    scripts: [
      createStructuredDataScript({
        type: 'WebSite',
        name: 'Your App',
        url: typeof window !== 'undefined' ? window.location.origin : 'https://yourapp.com',
        description: 'A modern, fast, and scalable web application built with TanStack Router and React.',
      }),
      createStructuredDataScript({
        type: 'Organization',
        name: 'Your App',
        url: typeof window !== 'undefined' ? window.location.origin : 'https://yourapp.com',
        description: 'A modern, fast, and scalable web application built with TanStack Router and React.',
        sameAs: [
          'https://twitter.com/yourapp',
          'https://github.com/yourapp',
          'https://linkedin.com/company/yourapp',
        ],
      }),
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <div className="p-2 flex gap-2 text-lg">
          <Link
            to="/"
            activeProps={{
              className: 'font-bold',
            }}
            activeOptions={{ exact: true }}
          >
            Home
          </Link>
        </div>
        <hr />
        {children}
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
