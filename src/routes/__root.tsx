import {
  Head<PERSON>ontent,
  <PERSON>,
  Outlet,
  <PERSON>rip<PERSON>,
  createRootRoute,
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import * as React from 'react'
import { DefaultCatchBoundary } from '~/components/DefaultCatchBoundary'
import { NotFound } from '~/components/NotFound'
import appCss from '~/styles/app.css?url'
import { seo, createStructuredDataScript } from '~/utils/seo'

export const Route = createRootRoute({
  head: () => ({
    title: 'Dịch vụ thuê xe sân bay uy tín, giá rẻ | Green Future',
    meta: [
      {
        charSet: 'utf-8',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1',
      },
      ...seo({
        title: 'Dịch vụ thuê xe sân bay uy tín, giá rẻ | Green Future',
        description: 'Dịch vụ thuê xe sân bay uy tín, g<PERSON><PERSON> cả hợp lý. <PERSON><PERSON> sân bay Tân <PERSON>ơ<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> Nẵng. Đặt xe <PERSON>, t<PERSON><PERSON> xế chuyên nghi<PERSON>, xe đời mới.',
        keywords: 'thuê xe sân bay, đón sân bay, xe sân bay Tân Sơn Nhất, xe sân bay Nội Bài, xe sân bay Đà Nẵng, Green Future, thuê xe giá rẻ, tài xế chuyên nghiệp',
        siteName: 'Green Future',
        type: 'website',
        locale: 'vi_VN',
        robots: 'index, follow',
        twitterCard: 'summary_large_image',
        author: 'Green Future',
      }),
    ],
    links: [
      { rel: 'stylesheet', href: appCss },
      {
        rel: 'apple-touch-icon',
        sizes: '180x180',
        href: '/apple-touch-icon.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
        href: '/favicon-32x32.png',
      },
      {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
        href: '/favicon-16x16.png',
      },
      { rel: 'manifest', href: '/site.webmanifest', color: '#fffff' },
      { rel: 'icon', href: '/favicon.ico' },
    ],
    scripts: [
      createStructuredDataScript({
        type: 'WebSite',
        name: 'Green Future',
        url: typeof window !== 'undefined' ? window.location.origin : 'https://greenfuture.vn',
        description: 'Dịch vụ thuê xe sân bay uy tín, giá cả hợp lý. Đón sân bay Tân Sơn Nhất, Nội Bài, Đà Nẵng. Đặt xe nhanh chóng, tài xế chuyên nghiệp, xe đời mới.',
      }),
      createStructuredDataScript({
        type: 'Organization',
        name: 'Green Future',
        url: typeof window !== 'undefined' ? window.location.origin : 'https://greenfuture.vn',
        description: 'Dịch vụ thuê xe sân bay uy tín, giá cả hợp lý tại Việt Nam. Chuyên cung cấp dịch vụ đón tiễn sân bay với đội ngũ tài xế chuyên nghiệp và xe đời mới.',
        sameAs: [
          'https://facebook.com/greenfuture',
          'https://zalo.me/greenfuture',
          'https://instagram.com/greenfuture',
        ],
      }),
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    )
  },
  notFoundComponent: () => <NotFound />,
  component: RootComponent,
})

function RootComponent() {
  return (
    <RootDocument>
      <Outlet />
    </RootDocument>
  )
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <HeadContent />
      </head>
      <body>
        <div className="p-2 flex gap-2 text-lg">
          <Link
            to="/"
            activeProps={{
              className: 'font-bold',
            }}
            activeOptions={{ exact: true }}
          >
            Home
          </Link>
        </div>
        <hr />
        {children}
        <TanStackRouterDevtools position="bottom-right" />
        <Scripts />
      </body>
    </html>
  )
}
