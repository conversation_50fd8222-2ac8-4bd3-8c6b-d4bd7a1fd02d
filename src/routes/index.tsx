import { createFileRoute } from '@tanstack/react-router'
import { seo } from '~/utils/seo'

export const Route = createFileRoute('/')({
  head: () => ({
    meta: [
      ...seo({
        title: 'Dịch vụ thuê xe sân bay uy tín, giá rẻ | Green Future',
        description: 'Dịch vụ thuê xe sân bay uy tín, giá cả hợp lý. Đón sân bay Tân Sơn Nhất, Nội Bài, Đà Nẵng. Đặt xe n<PERSON>h chóng, tài xế chuyên nghiệp, xe đời mới.',
        keywords: 'thuê xe sân bay, đón sân bay, xe sân bay Tân Sơn Nhất, xe sân bay Nội Bài, xe sân bay Đà Nẵng, Green Future, thuê xe giá rẻ, tài xế chuyên nghiệp',
        type: 'website',
        url: typeof window !== 'undefined' ? window.location.origin : undefined,
        canonical: typeof window !== 'undefined' ? window.location.origin : undefined,
      }),
    ],
  }),
  component: Home,
})

function Home() {
  return (
    <div className="p-2 max-w-4xl mx-auto">
      <header className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">
          Green Future - Dịch vụ thuê xe sân bay uy tín
        </h1>
        <p className="text-xl text-gray-600 mb-6">
          Dịch vụ đón tiễn sân bay chuyên nghiệp với giá cả hợp lý. Phục vụ sân bay Tân Sơn Nhất, Nội Bài, Đà Nẵng.
        </p>
        <p className="text-gray-500">
          Đặt xe nhanh chóng, tài xế chuyên nghiệp, xe đời mới. Cam kết đúng giờ, an toàn và tiết kiệm.
        </p>
      </header>

      <section className="grid md:grid-cols-2 gap-8 mb-8">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">
            Dịch vụ của chúng tôi
          </h2>
          <ul className="space-y-2 text-gray-600">
            <li>✈️ Đón tiễn sân bay Tân Sơn Nhất</li>
            <li>✈️ Đón tiễn sân bay Nội Bài</li>
            <li>✈️ Đón tiễn sân bay Đà Nẵng</li>
            <li>🚗 Xe đời mới, đầy đủ tiện nghi</li>
            <li>👨‍✈️ Tài xế chuyên nghiệp, kinh nghiệm</li>
            <li>💰 Giá cả hợp lý, minh bạch</li>
          </ul>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">
            Cam kết chất lượng
          </h2>
          <ul className="space-y-2 text-gray-600">
            <li>⏰ Đúng giờ, không chờ đợi</li>
            <li>🛡️ An toàn tuyệt đối</li>
            <li>📱 Đặt xe dễ dàng, nhanh chóng</li>
            <li>💬 Hỗ trợ 24/7</li>
            <li>🎯 Dịch vụ chuyên nghiệp</li>
            <li>💯 Khách hàng hài lòng</li>
          </ul>
        </div>
      </section>

      <section className="text-center">
        <h2 className="text-2xl font-semibold mb-4 text-gray-800">
          Sẵn sàng đặt xe ngay?
        </h2>
        <p className="text-gray-600 mb-6">
          Liên hệ với chúng tôi để được tư vấn và đặt xe với giá tốt nhất.
          Dịch vụ chuyên nghiệp, uy tín hàng đầu Việt Nam.
        </p>
        <div className="space-x-4">
          <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
            Đặt xe ngay
          </button>
          <button className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
            Xem bảng giá
          </button>
        </div>
      </section>
    </div>
  )
}
