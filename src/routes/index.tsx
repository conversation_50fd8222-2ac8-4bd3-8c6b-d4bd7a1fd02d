import { createFileRoute } from '@tanstack/react-router'
import { seo } from '~/utils/seo'

export const Route = createFileRoute('/')({
  head: () => ({
    meta: [
      ...seo({
        title: 'Home - Your App',
        description: 'Welcome to Your App - the best place to start building your next project. Fast, modern, and built with the latest technologies.',
        keywords: 'home, welcome, app, react, tanstack, modern web development',
        type: 'website',
        url: typeof window !== 'undefined' ? window.location.origin : undefined,
        canonical: typeof window !== 'undefined' ? window.location.origin : undefined,
      }),
    ],
  }),
  component: Home,
})

function Home() {
  return (
    <div className="p-2 max-w-4xl mx-auto">
      <header className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4 text-gray-900">
          Welcome to Your App
        </h1>
        <p className="text-xl text-gray-600 mb-6">
          A modern, fast, and scalable web application built with TanStack Router and React.
        </p>
        <p className="text-gray-500">
          This homepage includes comprehensive SEO optimization with meta tags, Open Graph,
          Twitter Cards, and structured data for better search engine visibility.
        </p>
      </header>

      <section className="grid md:grid-cols-2 gap-8 mb-8">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">
            SEO Features Included
          </h2>
          <ul className="space-y-2 text-gray-600">
            <li>✅ Meta tags for search engines</li>
            <li>✅ Open Graph tags for social sharing</li>
            <li>✅ Twitter Card optimization</li>
            <li>✅ Structured data (JSON-LD)</li>
            <li>✅ Canonical URLs</li>
            <li>✅ Robots meta tags</li>
          </ul>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h2 className="text-2xl font-semibold mb-3 text-gray-800">
            Built With Modern Tech
          </h2>
          <ul className="space-y-2 text-gray-600">
            <li>⚡ TanStack Router for routing</li>
            <li>⚛️ React 19 for UI</li>
            <li>🎨 Tailwind CSS for styling</li>
            <li>📱 Responsive design</li>
            <li>🚀 Fast performance</li>
            <li>🔍 SEO optimized</li>
          </ul>
        </div>
      </section>

      <section className="text-center">
        <h2 className="text-2xl font-semibold mb-4 text-gray-800">
          Ready to Build Something Amazing?
        </h2>
        <p className="text-gray-600 mb-6">
          Start customizing this application to fit your needs. Check out the documentation
          to learn more about implementing SEO and other features.
        </p>
        <div className="space-x-4">
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Get Started
          </button>
          <button className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors">
            View Documentation
          </button>
        </div>
      </section>
    </div>
  )
}
