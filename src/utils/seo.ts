export interface SEOProps {
  title: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: 'website' | 'article' | 'product' | 'profile'
  siteName?: string
  author?: string
  publishedTime?: string
  modifiedTime?: string
  section?: string
  tags?: string[]
  locale?: string
  alternateLocales?: string[]
  canonical?: string
  robots?: string
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player'
  twitterSite?: string
  twitterCreator?: string
}

export const seo = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  siteName = 'Your App',
  author,
  publishedTime,
  modifiedTime,
  section,
  tags,
  locale = 'en_US',
  alternateLocales,
  canonical,
  robots = 'index, follow',
  twitterCard = 'summary_large_image',
  twitterSite,
  twitterCreator,
}: SEOProps) => {
  const metaTags = [
    // Basic meta tags
    { title },
    { name: 'description', content: description },
    { name: 'keywords', content: keywords },
    { name: 'author', content: author },
    { name: 'robots', content: robots },

    // Open Graph tags
    { property: 'og:title', content: title },
    { property: 'og:description', content: description },
    { property: 'og:type', content: type },
    { property: 'og:site_name', content: siteName },
    { property: 'og:locale', content: locale },

    // Twitter Card tags
    { name: 'twitter:card', content: twitterCard },
    { name: 'twitter:title', content: title },
    { name: 'twitter:description', content: description },
    { name: 'twitter:site', content: twitterSite },
    { name: 'twitter:creator', content: twitterCreator },

    // Additional meta tags
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'theme-color', content: '#000000' },
    { name: 'msapplication-TileColor', content: '#000000' },
  ].filter(tag => tag.content !== undefined && tag.content !== null && tag.content !== '')

  // Add URL if provided
  if (url) {
    metaTags.push({ property: 'og:url', content: url })
    metaTags.push({ name: 'twitter:url', content: url })
  }

  // Add canonical URL if provided
  if (canonical) {
    metaTags.push({ property: 'canonical', content: canonical })
  }

  // Add image tags if provided
  if (image) {
    metaTags.push(
      { property: 'og:image', content: image },
      { property: 'og:image:alt', content: title },
      { name: 'twitter:image', content: image },
      { name: 'twitter:image:alt', content: title }
    )
  }

  // Add article-specific tags
  if (type === 'article') {
    if (author) metaTags.push({ property: 'article:author', content: author })
    if (publishedTime) metaTags.push({ property: 'article:published_time', content: publishedTime })
    if (modifiedTime) metaTags.push({ property: 'article:modified_time', content: modifiedTime })
    if (section) metaTags.push({ property: 'article:section', content: section })
    if (tags) {
      tags.forEach(tag => {
        metaTags.push({ property: 'article:tag', content: tag })
      })
    }
  }

  // Add alternate locales
  if (alternateLocales) {
    alternateLocales.forEach(locale => {
      metaTags.push({ property: 'og:locale:alternate', content: locale })
    })
  }

  return metaTags
}

/**
 * Generate structured data (JSON-LD) for better SEO
 */
export interface StructuredDataProps {
  type: 'WebSite' | 'Organization' | 'Person' | 'Article' | 'Product' | 'BreadcrumbList'
  name?: string
  url?: string
  logo?: string
  description?: string
  author?: string | { name: string; url?: string }
  publisher?: { name: string; logo?: string }
  datePublished?: string
  dateModified?: string
  headline?: string
  image?: string | string[]
  sameAs?: string[]
  contactPoint?: {
    telephone?: string
    contactType?: string
    email?: string
  }
  address?: {
    streetAddress?: string
    addressLocality?: string
    addressRegion?: string
    postalCode?: string
    addressCountry?: string
  }
  breadcrumbs?: Array<{
    name: string
    url: string
  }>
}

export const generateStructuredData = (props: StructuredDataProps) => {
  const baseData = {
    '@context': 'https://schema.org',
    '@type': props.type,
  }

  switch (props.type) {
    case 'WebSite':
      return {
        ...baseData,
        name: props.name,
        url: props.url,
        description: props.description,
        potentialAction: {
          '@type': 'SearchAction',
          target: `${props.url}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      }

    case 'Organization':
      return {
        ...baseData,
        name: props.name,
        url: props.url,
        logo: props.logo,
        description: props.description,
        sameAs: props.sameAs,
        contactPoint: props.contactPoint,
        address: props.address,
      }

    case 'Article':
      return {
        ...baseData,
        headline: props.headline,
        description: props.description,
        image: props.image,
        author: props.author,
        publisher: props.publisher,
        datePublished: props.datePublished,
        dateModified: props.dateModified,
      }

    case 'BreadcrumbList':
      return {
        ...baseData,
        itemListElement: props.breadcrumbs?.map((crumb, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: crumb.name,
          item: crumb.url,
        })),
      }

    default:
      return baseData
  }
}

/**
 * Create a script tag for structured data
 */
export const createStructuredDataScript = (data: StructuredDataProps) => {
  return {
    type: 'application/ld+json',
    children: JSON.stringify(generateStructuredData(data)),
  }
}
